# utils.py

import sys
import os
import pygame

def resource_path(relative_path):
    """ Get absolute path to resource, works for dev and for PyInstaller """
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(sys._MEIPASS, relative_path)
    return os.path.join(os.path.abspath("."), relative_path)

def load_image(path):
    """Load an image using pygame with resource_path."""
    return pygame.image.load(resource_path(path))

def load_sound(path):
    """Load a sound using pygame with resource_path."""
    return pygame.mixer.Sound(resource_path(path))

def load_font(path, size):
    """Load a font file (.ttf) or system font using pygame. Always prefer system font for Polish support."""
    # Always use system font for best Unicode/Polish support
    # If path is a font file, fallback to system font if loading fails
    try:
        if path.lower().endswith(('.ttf', '.otf')):
            # Try loading TTF, but fallback to system font if error
            try:
                return pygame.font.Font(resource_path(path), size)
            except Exception:
                pass
        # Use system font (Arial or DejaVu Sans for Polish)
        return pygame.font.SysFont("DejaVu Sans", size) or pygame.font.SysFont("Arial", size)
    except Exception:
        # As a last resort, use default font
        return pygame.font.SysFont(None, size)
