import pygame
from background import Background
from shop import ShopCard, TabButton, ConfirmDialog, save_purchase
from ui_components import TextButton
import json
from utils import load_font

def draw_shop_screen(s_width, s_height, screen):
    run = True
    bg = Background('background', s_width, s_height)
    clock = pygame.time.Clock()
    scroll_y = 0
    
    # Load data
    with open('data/shop_data.json') as f:
        shop_data = json.load(f)
    parts_data = shop_data[0]
    cars_data = shop_data[1]['cars']
    with open('data/profile.json') as f:
        profile_data = json.load(f)
    
    # Create buttons
    back_button = TextButton('Powrót', 50, 50, font_size=36)
    
    # Create tabs
    tab_width = 150
    tab_height = 50
    tab_y = 120
    cars_tab = TabButton('Samochody', s_width // 2 - tab_width - 10, tab_y, tab_width, tab_height)
    parts_tab = TabButton('Części', s_width // 2 + 10, tab_y, tab_width, tab_height)
    
    # Set initial tab
    current_tab = "cars"
    cars_tab.is_active = True
    
    # Create fonts
    header_font = load_font("arial", 48)
    info_font = load_font("arial", 24)
    money_font = load_font("arial", 36)
    
    header = header_font.render("Sklep", True, (255, 255, 255))
    
    # Dialog state
    confirm_dialog = None
    selected_item = None
    selected_item_type = None
    
    while run:
        mouse_pos = pygame.mouse.get_pos()
        mouse_click = pygame.mouse.get_pressed()
        dt = clock.tick(60) / 1000.0
        
        # Handle events and scrolling
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return
            if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                if confirm_dialog:
                    confirm_dialog = None
                else:
                    return
            if event.type == pygame.MOUSEWHEEL:
                scroll_y = max(0, scroll_y - event.y * 30)
        
        # Keyboard scrolling
        keys = pygame.key.get_pressed()
        if keys[pygame.K_UP]:
            scroll_y = max(0, scroll_y - 10)
        if keys[pygame.K_DOWN]:
            scroll_y += 10
        
        # Update buttons
        back_button.update(mouse_pos, mouse_click)
        
        # Check back button
        if back_button.is_hovered and mouse_click[0] and not confirm_dialog:
            return
        
        # Handle dialogs
        if confirm_dialog:
            dialog_result = confirm_dialog.update(mouse_pos, mouse_click)
            if dialog_result == "confirm":
                try:
                    # save_purchase handles money deduction and validation
                    save_purchase(selected_item, selected_item_type)
                    # Reload profile data to get updated money amount
                    with open('data/profile.json') as f:
                        profile_data = json.load(f)
                except ValueError as e:
                    print(f"Purchase failed: {e}")
                except Exception as e:
                    print(f"Error during purchase: {e}")
                confirm_dialog = None
                selected_item = None
                selected_item_type = None
            elif dialog_result == "cancel":
                confirm_dialog = None
                selected_item = None
                selected_item_type = None
        else:
            # Update tabs
            if cars_tab.update(mouse_pos, mouse_click):
                current_tab = "cars"
                cars_tab.is_active = True
                parts_tab.is_active = False
                scroll_y = 0  # Reset scroll when changing tabs
            
            if parts_tab.update(mouse_pos, mouse_click):
                current_tab = "parts"
                cars_tab.is_active = False
                parts_tab.is_active = True
                scroll_y = 0  # Reset scroll when changing tabs
        
        # Get items for current tab
        if current_tab == "cars":
            items = cars_data
            item_type = "car"
        else:
            items = []
            for category, parts_list in parts_data.items():
                for part in parts_list:
                    part['category'] = category
                    items.append(part)
            item_type = "part"
        
        # Create cards layout
        cards_per_row = 4
        card_width = 250
        card_height = 300 if current_tab == "cars" else 200
        card_spacing = 30
        start_x = (s_width - (cards_per_row * card_width + (cards_per_row - 1) * card_spacing)) // 2
        start_y = 200
        
        # Calculate max scroll
        total_rows = (len(items) + cards_per_row - 1) // cards_per_row
        content_height = total_rows * (card_height + card_spacing)
        max_scroll = max(0, content_height - (s_height - start_y))
        scroll_y = min(scroll_y, max_scroll)
        
        # Create and update shop cards
        shop_cards = []
        for i, item in enumerate(items):
            row = i // cards_per_row
            col = i % cards_per_row
            x = start_x + col * (card_width + card_spacing)
            y = start_y + row * (card_height + card_spacing) - scroll_y
            
            # Skip cards that are completely outside the visible area
            if y + card_height < start_y or y > s_height:
                continue
            
            # Check if item is owned
            is_owned = False
            if item_type == "car":
                is_owned = item["name"] in profile_data["inventory"]["owned_cars"]
            else:
                is_owned = item["name"] in profile_data["inventory"]["owned_parts"][item["category"]]
            
            card = ShopCard(item, x, y, card_width, card_height, item_type, is_owned)
            
            # Check if card was clicked
            if not confirm_dialog and card.update(mouse_pos, mouse_click):
                if not is_owned and profile_data['money'] >= item['value']:
                    selected_item = item
                    selected_item_type = item_type
                    # Show confirm dialog for all items (color system removed)
                    confirm_dialog = ConfirmDialog(item['name'], item['value'], s_width, s_height)
            
            shop_cards.append(card)
        
        # Draw everything
        bg.draw(screen)
        
        # Draw fixed UI elements
        screen.blit(header, (s_width // 2 - header.get_width() // 2, 50))
        money_text = money_font.render(f"Pieniądze: {profile_data['money']} $", True, (255, 255, 0))
        screen.blit(money_text, (s_width - money_text.get_width() - 50, 50))
        cars_tab.draw(screen)
        parts_tab.draw(screen)
        back_button.draw(screen)
        
        # Draw shop cards
        for card in shop_cards:
            card.draw(screen)
        
        # Draw dialogs if active
        if confirm_dialog:
            # Draw overlay
            overlay = pygame.Surface((s_width, s_height))
            overlay.set_alpha(128)
            overlay.fill((0, 0, 0))
            screen.blit(overlay, (0, 0))
            confirm_dialog.draw(screen)
        
        pygame.display.update()
