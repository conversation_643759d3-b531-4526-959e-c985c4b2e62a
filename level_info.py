import pygame
import json
from background import Background
from ui_components import TextButton
from utils import load_font

def draw_level_info_screen(s_width, s_height, screen, level):
    """
    Display level information before the race
    """
    run = True
    bg = Background('background', s_width, s_height)
    
    # Load opponent data
    try:
        with open('data/oponent_levels.json', 'r', encoding='utf-8') as f:
            opponents_data = json.load(f)
        
        if level <= len(opponents_data):
            opponent = opponents_data[level - 1]
        else:
            # Fallback for levels beyond available data
            opponent = {
                "opponent_name": f"Poziom {level}",
                "level": level,
                "parts": {"engine": {"horsepower": 100}}
            }
    except:
        opponent = {
            "opponent_name": f"Poziom {level}",
            "level": level,
            "parts": {"engine": {"horsepower": 100}}
        }
    
    # Create buttons
    start_race_button = TextButton('Rozpocznij Wyścig', s_width // 2 - 150, s_height - 150, font_size=36)
    back_button = TextButton('Powrót', 50, 50, font_size=36)
    
    # Create fonts
    header_font = load_font("arial", 64)
    title_font = load_font("arial", 48)
    info_font = load_font("arial", 32)
    detail_font = load_font("arial", 24)
    
    while run:
        mouse_pos = pygame.mouse.get_pos()
        mouse_click = pygame.mouse.get_pressed()
        
        # Handle events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return "quit"
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    return "back"
                elif event.key == pygame.K_RETURN or event.key == pygame.K_SPACE:
                    return "start_race"
        
        # Update buttons
        start_race_button.update(mouse_pos, mouse_click)
        back_button.update(mouse_pos, mouse_click)
        
        # Check button clicks
        if start_race_button.is_hovered and mouse_click[0]:
            return "start_race"
        if back_button.is_hovered and mouse_click[0]:
            return "back"
        
        # Draw everything
        bg.draw(screen)
        
        # Dynamic vertical positioning
        y = 60
        spacing = 18
        
        # Draw header
        header_text = header_font.render(f"POZIOM {level}", True, (255, 255, 0))
        header_x = (s_width - header_text.get_width()) // 2
        screen.blit(header_text, (header_x, y))
        y += header_text.get_height() + spacing
        
        # Draw opponent name
        opponent_text = title_font.render(f"Przeciwnik: {opponent.get('opponent_name', 'Nieznany')}", True, (255, 255, 255))
        opponent_x = (s_width - opponent_text.get_width()) // 2
        screen.blit(opponent_text, (opponent_x, y))
        y += opponent_text.get_height() + spacing
        
        # Calculate opponent stats
        engine = opponent.get("parts", {}).get("engine", {})
        base_hp = engine.get("horsepower", 100)
        
        # Calculate boosts
        total_boost = 0
        parts = opponent.get("parts", {})
        
        for part_type in ["turbo", "intercooler", "ecu"]:
            part = parts.get(part_type)
            if part and "horsepower_boost_percentage" in part:
                total_boost += part["horsepower_boost_percentage"]
        
        total_hp = int(base_hp * (1 + total_boost / 100))
        weight = opponent.get("weight", 500)
        
        # Calculate total weight with parts
        total_weight = weight
        for part_type, part in parts.items():
            if part and "weight" in part:
                total_weight += part["weight"]
        
        power_to_weight = total_hp / total_weight if total_weight > 0 else 0
        
        # Draw opponent stats
        stats = [
            f"Moc silnika: {total_hp} KM",
            f"Waga pojazdu: {total_weight} kg",
            f"Stosunek mocy do wagi: {power_to_weight:.2f}",
            f"Trudność: {'★' * min(level, 10)}"
        ]
        for stat in stats:
            stat_text = info_font.render(stat, True, (200, 200, 200))
            stat_x = (s_width - stat_text.get_width()) // 2
            screen.blit(stat_text, (stat_x, y))
            y += stat_text.get_height() + 6  # mniejszy odstęp, by zmieścić więcej
        y += spacing
        
        # Draw parts info
        parts_title = detail_font.render("Części przeciwnika:", True, (255, 255, 255))
        parts_x = (s_width - parts_title.get_width()) // 2
        screen.blit(parts_title, (parts_x, y))
        y += parts_title.get_height() + 4
        
        parts_info = []
        for part_type, part in parts.items():
            if part:
                part_name = part.get("name", "Nieznana część")
                parts_info.append(f"{part_type.title()}: {part_name}")
            else:
                parts_info.append(f"{part_type.title()}: Brak")
        for part_info in parts_info:
            part_text = detail_font.render(part_info, True, (150, 150, 150))
            part_x = (s_width - part_text.get_width()) // 2
            screen.blit(part_text, (part_x, y))
            y += part_text.get_height() + 2
        y += spacing * 2
        
        # Draw instructions
        instruction_text = detail_font.render("Naciśnij ENTER lub SPACJĘ aby rozpocząć wyścig", True, (255, 255, 0))
        instruction_x = (s_width - instruction_text.get_width()) // 2
        screen.blit(instruction_text, (instruction_x, s_height - 100))
        
        # Draw buttons
        start_race_button.draw(screen)
        back_button.draw(screen)
        
        pygame.display.update()
    
    return "back"


def draw_level_complete_screen(s_width, s_height, screen, level, reward, new_level):
    """
    Display level completion screen
    """
    run = True
    bg = Background('background', s_width, s_height)
    
    # Create buttons
    continue_button = TextButton('Kontynuuj', s_width // 2 - 100, s_height - 150, font_size=36)
    
    # Create fonts
    header_font = load_font("arial", 64)
    info_font = load_font("arial", 36)
    detail_font = load_font("arial", 24)
    
    # Show for 3 seconds automatically or until clicked
    start_time = pygame.time.get_ticks()
    auto_continue_time = 3000  # 3 seconds
    
    while run:
        mouse_pos = pygame.mouse.get_pos()
        mouse_click = pygame.mouse.get_pressed()
        current_time = pygame.time.get_ticks()
        
        # Handle events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return "quit"
            if event.type == pygame.KEYDOWN or mouse_click[0]:
                return "continue"
        
        # Auto-continue after 3 seconds
        if current_time - start_time > auto_continue_time:
            return "continue"
        
        # Update button
        continue_button.update(mouse_pos, mouse_click)
        
        # Check button click
        if continue_button.is_hovered and mouse_click[0]:
            return "continue"
        
        # Draw everything
        bg.draw(screen)
        
        # Dynamic vertical positioning
        y = 120
        spacing = 24
        
        # Draw header
        header_text = header_font.render(f"POZIOM {level} UKOŃCZONY!", True, (0, 255, 0))
        header_x = (s_width - header_text.get_width()) // 2
        screen.blit(header_text, (header_x, y))
        y += header_text.get_height() + spacing
        
        # Draw rewards
        reward_text = info_font.render(f"Nagroda: {reward} $", True, (255, 255, 0))
        reward_x = (s_width - reward_text.get_width()) // 2
        screen.blit(reward_text, (reward_x, y))
        y += reward_text.get_height() + 8
        
        if new_level > level:
            level_up_text = info_font.render(f"AWANS NA POZIOM {new_level}!", True, (255, 255, 0))
            level_up_x = (s_width - level_up_text.get_width()) // 2
            screen.blit(level_up_text, (level_up_x, y))
            y += level_up_text.get_height() + 8
        y += spacing
        
        # Draw countdown
        remaining_time = max(0, auto_continue_time - (current_time - start_time)) / 1000
        countdown_text = detail_font.render(f"Automatyczne przejście za: {remaining_time:.1f}s", True, (200, 200, 200))
        countdown_x = (s_width - countdown_text.get_width()) // 2
        screen.blit(countdown_text, (countdown_x, y))
        y += countdown_text.get_height() + spacing
        
        # Draw instruction
        instruction_text = detail_font.render("Kliknij lub naciśnij dowolny klawisz aby kontynuować", True, (255, 255, 255))
        instruction_x = (s_width - instruction_text.get_width()) // 2
        screen.blit(instruction_text, (instruction_x, s_height - 120))
        
        # Draw button
        continue_button.draw(screen)
        
        pygame.display.update()
    
    return "continue"
