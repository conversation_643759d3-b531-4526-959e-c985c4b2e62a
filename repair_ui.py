import pygame
import json
from background import Background
from ui_components import TextButton
from maintenance_system import maintenance_system
from valuation_system import valuation_system
from utils import load_font

def draw_repair_screen(s_width, s_height, screen, car_index):
    """Draw the car repair screen"""
    run = True
    bg = Background('background', s_width, s_height)
    
    # Load car and profile data
    try:
        with open('data/garage.json') as f:
            cars_data = json.load(f)
        with open('data/profile.json') as f:
            profile_data = json.load(f)
    except Exception as e:
        print(f"Error loading data: {e}")
        return
    
    if car_index >= len(cars_data):
        return
    
    car_data = cars_data[car_index]
    
    # Get current usage data and condition
    usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(str(car_index), 
                                                                        valuation_system.get_default_usage_data())
    
    # Calculate current condition
    performance_data = valuation_system.calculate_enhanced_performance(car_data, usage_data)
    current_condition = performance_data["condition_effects"]["avg_parts_condition"]
    
    # Create buttons
    back_button = TextButton('Powrót', 50, 50, font_size=36)
    maintenance_button = TextButton('Konserwacja', s_width // 2 - 200, s_height - 150, font_size=32)
    
    # Get repair options
    repair_options = maintenance_system.get_repair_options(car_index)
    repair_buttons = []
    
    for i, option in enumerate(repair_options):
        x = 100 + i * 250
        y = s_height // 2 + 100
        button = TextButton(f"{option['name']}\n{option['cost']} $", x, y, font_size=24, width=200, height=80)
        repair_buttons.append((button, option))
    
    # Create fonts
    header_font = load_font("arial", 48)
    info_font = load_font("arial", 32)
    detail_font = load_font("arial", 24)
    
    # Message display
    message = ""
    message_color = (255, 255, 255)
    message_timer = 0
    
    while run:
        dt = pygame.time.Clock().tick(60) / 1000.0
        mouse_pos = pygame.mouse.get_pos()
        mouse_click = pygame.mouse.get_pressed()
        
        # Update message timer
        if message_timer > 0:
            message_timer -= dt
            if message_timer <= 0:
                message = ""
        
        # Handle events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return
            if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                return
        
        # Update buttons
        back_button.update(mouse_pos, mouse_click)
        maintenance_button.update(mouse_pos, mouse_click)
        
        # Check back button
        if back_button.is_hovered and mouse_click[0]:
            return
        
        # Check maintenance button with debouncing
        if maintenance_button.is_hovered and mouse_click[0]:
            current_time = pygame.time.get_ticks()
            if not hasattr(maintenance_system.perform_maintenance, 'last_click_time'):
                maintenance_system.perform_maintenance.last_click_time = 0

            if current_time - maintenance_system.perform_maintenance.last_click_time > 500:  # 500ms debounce
                maintenance_system.perform_maintenance.last_click_time = current_time
                success, msg = maintenance_system.perform_maintenance(car_index)
                if success:
                    message = msg
                    message_color = (0, 255, 0)
                    message_timer = 3.0
                    # Reload data
                    with open('data/profile.json') as f:
                        profile_data = json.load(f)
                    usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(str(car_index),
                                                                                        valuation_system.get_default_usage_data())
                    performance_data = valuation_system.calculate_enhanced_performance(car_data, usage_data)
                    current_condition = performance_data["condition_effects"]["avg_parts_condition"]
                else:
                    message = msg
                    message_color = (255, 0, 0)
                    message_timer = 3.0
        
        # Check repair buttons with debouncing
        for button, option in repair_buttons:
            button.update(mouse_pos, mouse_click)
            if button.is_hovered and mouse_click[0]:
                current_time = pygame.time.get_ticks()
                repair_key = f"repair_{option['type']}"
                if not hasattr(maintenance_system.repair_car, 'last_click_times'):
                    maintenance_system.repair_car.last_click_times = {}

                if repair_key not in maintenance_system.repair_car.last_click_times:
                    maintenance_system.repair_car.last_click_times[repair_key] = 0

                if current_time - maintenance_system.repair_car.last_click_times[repair_key] > 600:  # 600ms debounce for repairs
                    maintenance_system.repair_car.last_click_times[repair_key] = current_time
                    success, msg = maintenance_system.repair_car(car_index, option['type'])
                    if success:
                        message = msg
                        message_color = (0, 255, 0)
                        message_timer = 3.0
                        # Reload data
                        with open('data/profile.json') as f:
                            profile_data = json.load(f)
                        usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(str(car_index),
                                                                                            valuation_system.get_default_usage_data())
                        performance_data = valuation_system.calculate_enhanced_performance(car_data, usage_data)
                        current_condition = performance_data["condition_effects"]["avg_parts_condition"]
                    else:
                        message = msg
                        message_color = (255, 0, 0)
                        message_timer = 3.0
        
        # Draw everything
        bg.draw(screen)
        
        # Draw header
        header = header_font.render("Warsztat Samochodowy", True, (255, 255, 255))
        screen.blit(header, (s_width // 2 - header.get_width() // 2, 50))
        
        # Draw car info
        car_name = info_font.render(f"Samochód: {car_data['name'].title()}", True, (255, 255, 255))
        screen.blit(car_name, (100, 150))
        
        # Draw condition info
        condition_percent = int(current_condition * 100)
        if condition_percent >= 80:
            condition_color = (0, 255, 0)  # Green
        elif condition_percent >= 60:
            condition_color = (255, 255, 0)  # Yellow
        elif condition_percent >= 40:
            condition_color = (255, 165, 0)  # Orange
        else:
            condition_color = (255, 0, 0)  # Red
        
        condition_text = info_font.render(f"Stan ogólny: {condition_percent}%", True, condition_color)
        screen.blit(condition_text, (100, 200))
        
        # Draw money
        money_text = info_font.render(f"Pieniądze: {profile_data['money']} $", True, (255, 255, 0))
        screen.blit(money_text, (s_width - money_text.get_width() - 100, 150))
        
        # Draw repair options header
        repair_header = info_font.render("Opcje naprawy:", True, (255, 255, 255))
        screen.blit(repair_header, (100, s_height // 2 + 50))
        
        # Draw repair option details
        for i, (button, option) in enumerate(repair_buttons):
            button.draw(screen)
            
            # Draw option description below button
            desc_y = button.rect.y + button.rect.height + 10
            desc_text = detail_font.render(option['description'], True, (200, 200, 200))
            screen.blit(desc_text, (button.rect.x, desc_y))
        
        # Draw maintenance button
        maintenance_button.draw(screen)
        
        # Draw maintenance info
        maint_info = detail_font.render("Konserwacja: Podstawowa poprawa stanu (-5 dni wieku)", True, (200, 200, 200))
        screen.blit(maint_info, (maintenance_button.rect.x, maintenance_button.rect.y + maintenance_button.rect.height + 10))
        
        # Draw buttons
        back_button.draw(screen)
        
        # Draw message if any
        if message and message_timer > 0:
            msg_text = info_font.render(message, True, message_color)
            screen.blit(msg_text, (s_width // 2 - msg_text.get_width() // 2, s_height - 50))
        
        pygame.display.update()
